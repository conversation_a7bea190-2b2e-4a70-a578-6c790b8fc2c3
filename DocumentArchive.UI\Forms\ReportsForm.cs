using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Helpers;

namespace DocumentArchive.UI.Forms
{
    public partial class ReportsForm : Form
    {
        private readonly IDocumentService _documentService;
        private readonly ICategoryService _categoryService;
        private Dictionary<string, int> _currentStatistics;

        public ReportsForm(IDocumentService documentService, ICategoryService categoryService)
        {
            _documentService = documentService ?? throw new ArgumentNullException(nameof(documentService));
            _categoryService = categoryService ?? throw new ArgumentNullException(nameof(categoryService));
            
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Configure form for Arabic
            UIHelper.ConfigureFormForArabic(this);
            
            // Style buttons
            UIHelper.StylePrimaryButton(generateReportButton);
            UIHelper.StyleSecondaryButton(exportExcelButton);
            UIHelper.StyleSecondaryButton(exportPdfButton);
            UIHelper.StyleSecondaryButton(refreshButton);
            UIHelper.StyleSecondaryButton(closeButton);
            
            // Style data grid views
            UIHelper.StyleDataGridView(statisticsDataGridView);
            UIHelper.StyleDataGridView(categoryStatsDataGridView);
            UIHelper.StyleDataGridView(monthlyStatsDataGridView);
            
            // Initialize date pickers
            fromDatePicker.Value = DateTime.Now.AddMonths(-1);
            toDatePicker.Value = DateTime.Now;
            
            // Initialize report type combo
            reportTypeComboBox.Items.AddRange(new object[]
            {
                new { Value = "summary", Text = "تقرير ملخص" },
                new { Value = "detailed", Text = "تقرير مفصل" },
                new { Value = "category", Text = "تقرير حسب الفئة" },
                new { Value = "monthly", Text = "تقرير شهري" },
                new { Value = "status", Text = "تقرير حسب الحالة" }
            });
            reportTypeComboBox.DisplayMember = "Text";
            reportTypeComboBox.ValueMember = "Value";
            reportTypeComboBox.SelectedIndex = 0;
        }

        private async void ReportsForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadStatisticsAsync();
                await LoadCategoryStatisticsAsync();
                await LoadMonthlyStatisticsAsync();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الإحصائيات");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                _currentStatistics = await _documentService.GetStatisticsAsync();
                DisplayGeneralStatistics();
                CreateStatisticsChart();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الإحصائيات العامة");
            }
        }

        private void DisplayGeneralStatistics()
        {
            if (_currentStatistics == null) return;

            // Update summary labels
            totalDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("TotalDocuments", 0).ToString();
            incomingDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("IncomingDocuments", 0).ToString();
            outgoingDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("OutgoingDocuments", 0).ToString();
            draftDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("DraftDocuments", 0).ToString();
            processedDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("ProcessedDocuments", 0).ToString();
            archivedDocumentsLabel.Text = _currentStatistics.GetValueOrDefault("ArchivedDocuments", 0).ToString();

            // Create statistics data for grid
            var statisticsData = new List<dynamic>
            {
                new { Category = "إجمالي الوثائق", Count = _currentStatistics.GetValueOrDefault("TotalDocuments", 0) },
                new { Category = "الوثائق الواردة", Count = _currentStatistics.GetValueOrDefault("IncomingDocuments", 0) },
                new { Category = "الوثائق الصادرة", Count = _currentStatistics.GetValueOrDefault("OutgoingDocuments", 0) },
                new { Category = "المسودات", Count = _currentStatistics.GetValueOrDefault("DraftDocuments", 0) },
                new { Category = "المعالجة", Count = _currentStatistics.GetValueOrDefault("ProcessedDocuments", 0) },
                new { Category = "المؤرشفة", Count = _currentStatistics.GetValueOrDefault("ArchivedDocuments", 0) }
            };

            statisticsDataGridView.DataSource = statisticsData;
            ConfigureStatisticsGrid();
        }

        private void ConfigureStatisticsGrid()
        {
            if (statisticsDataGridView.Columns.Count == 0) return;

            statisticsDataGridView.Columns["Category"].HeaderText = "الفئة";
            statisticsDataGridView.Columns["Category"].Width = 200;
            statisticsDataGridView.Columns["Count"].HeaderText = "العدد";
            statisticsDataGridView.Columns["Count"].Width = 100;
            statisticsDataGridView.Columns["Count"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private async Task LoadCategoryStatisticsAsync()
        {
            try
            {
                var categories = await _categoryService.GetAllAsync();
                var categoryStats = categories.Select(c => new
                {
                    CategoryName = c.Name,
                    DocumentCount = c.DocumentCount,
                    IsActive = c.IsActive ? "نشطة" : "غير نشطة"
                }).ToList();

                categoryStatsDataGridView.DataSource = categoryStats;
                ConfigureCategoryStatsGrid();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل إحصائيات الفئات");
            }
        }

        private void ConfigureCategoryStatsGrid()
        {
            if (categoryStatsDataGridView.Columns.Count == 0) return;

            categoryStatsDataGridView.Columns["CategoryName"].HeaderText = "اسم الفئة";
            categoryStatsDataGridView.Columns["CategoryName"].Width = 200;
            categoryStatsDataGridView.Columns["DocumentCount"].HeaderText = "عدد الوثائق";
            categoryStatsDataGridView.Columns["DocumentCount"].Width = 100;
            categoryStatsDataGridView.Columns["DocumentCount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            categoryStatsDataGridView.Columns["IsActive"].HeaderText = "الحالة";
            categoryStatsDataGridView.Columns["IsActive"].Width = 80;
            categoryStatsDataGridView.Columns["IsActive"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private async Task LoadMonthlyStatisticsAsync()
        {
            try
            {
                // Get documents from last 12 months
                var fromDate = DateTime.Now.AddMonths(-12);
                var toDate = DateTime.Now;
                
                var criteria = new SearchCriteriaDto
                {
                    DateFrom = fromDate,
                    DateTo = toDate,
                    PageSize = int.MaxValue,
                    PageNumber = 1
                };

                var documents = await _documentService.GetAllAsync(criteria);
                
                var monthlyStats = documents.Items
                    .GroupBy(d => new { d.Date.Year, d.Date.Month })
                    .Select(g => new
                    {
                        Month = $"{g.Key.Year}/{g.Key.Month:D2}",
                        TotalDocuments = g.Count(),
                        IncomingDocuments = g.Count(d => d.Type == DocumentType.Incoming),
                        OutgoingDocuments = g.Count(d => d.Type == DocumentType.Outgoing)
                    })
                    .OrderBy(x => x.Month)
                    .ToList();

                monthlyStatsDataGridView.DataSource = monthlyStats;
                ConfigureMonthlyStatsGrid();
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تحميل الإحصائيات الشهرية");
            }
        }

        private void ConfigureMonthlyStatsGrid()
        {
            if (monthlyStatsDataGridView.Columns.Count == 0) return;

            monthlyStatsDataGridView.Columns["Month"].HeaderText = "الشهر";
            monthlyStatsDataGridView.Columns["Month"].Width = 100;
            monthlyStatsDataGridView.Columns["TotalDocuments"].HeaderText = "إجمالي الوثائق";
            monthlyStatsDataGridView.Columns["TotalDocuments"].Width = 120;
            monthlyStatsDataGridView.Columns["TotalDocuments"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            monthlyStatsDataGridView.Columns["IncomingDocuments"].HeaderText = "الواردة";
            monthlyStatsDataGridView.Columns["IncomingDocuments"].Width = 80;
            monthlyStatsDataGridView.Columns["IncomingDocuments"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            monthlyStatsDataGridView.Columns["OutgoingDocuments"].HeaderText = "الصادرة";
            monthlyStatsDataGridView.Columns["OutgoingDocuments"].Width = 80;
            monthlyStatsDataGridView.Columns["OutgoingDocuments"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
        }

        private void CreateStatisticsChart()
        {
            try
            {
                // Simple text-based chart for now
                var chartText = "الإحصائيات المرئية:\n\n";
                
                if (_currentStatistics != null)
                {
                    var total = _currentStatistics.GetValueOrDefault("TotalDocuments", 0);
                    var incoming = _currentStatistics.GetValueOrDefault("IncomingDocuments", 0);
                    var outgoing = _currentStatistics.GetValueOrDefault("OutgoingDocuments", 0);
                    
                    if (total > 0)
                    {
                        var incomingPercent = (incoming * 100) / total;
                        var outgoingPercent = (outgoing * 100) / total;
                        
                        chartText += $"الوثائق الواردة: {incoming} ({incomingPercent}%)\n";
                        chartText += $"الوثائق الصادرة: {outgoing} ({outgoingPercent}%)\n\n";
                        
                        // Simple bar representation
                        chartText += "التمثيل المرئي:\n";
                        chartText += $"الواردة  : {new string('█', Math.Min(incomingPercent, 50))}\n";
                        chartText += $"الصادرة : {new string('█', Math.Min(outgoingPercent, 50))}\n";
                    }
                }
                
                chartTextBox.Text = chartText;
            }
            catch (Exception ex)
            {
                chartTextBox.Text = $"فشل في إنشاء الرسم البياني: {ex.Message}";
            }
        }

        private async void generateReportButton_Click(object sender, EventArgs e)
        {
            await GenerateReportAsync();
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                var reportType = ((dynamic)reportTypeComboBox.SelectedItem)?.Value?.ToString();
                var fromDate = fromDatePicker.Value.Date;
                var toDate = toDatePicker.Value.Date;

                if (fromDate > toDate)
                {
                    MessageHelper.ShowError("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");
                    return;
                }

                var loadingForm = MessageHelper.ShowLoadingMessage("جاري إنشاء التقرير...");
                
                try
                {
                    var criteria = new SearchCriteriaDto
                    {
                        DateFrom = fromDate,
                        DateTo = toDate,
                        PageSize = int.MaxValue,
                        PageNumber = 1
                    };

                    var documents = await _documentService.GetAllAsync(criteria);
                    
                    switch (reportType)
                    {
                        case "summary":
                            GenerateSummaryReport(documents.Items, fromDate, toDate);
                            break;
                        case "detailed":
                            GenerateDetailedReport(documents.Items, fromDate, toDate);
                            break;
                        case "category":
                            GenerateCategoryReport(documents.Items, fromDate, toDate);
                            break;
                        case "monthly":
                            GenerateMonthlyReport(documents.Items, fromDate, toDate);
                            break;
                        case "status":
                            GenerateStatusReport(documents.Items, fromDate, toDate);
                            break;
                        default:
                            GenerateSummaryReport(documents.Items, fromDate, toDate);
                            break;
                    }
                }
                finally
                {
                    MessageHelper.CloseLoadingMessage(loadingForm);
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في إنشاء التقرير");
            }
        }

        private void GenerateSummaryReport(List<DocumentDto> documents, DateTime fromDate, DateTime toDate)
        {
            var report = $"تقرير ملخص الوثائق\n";
            report += $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            report += $"إجمالي الوثائق: {documents.Count}\n";
            report += $"الوثائق الواردة: {documents.Count(d => d.Type == DocumentType.Incoming)}\n";
            report += $"الوثائق الصادرة: {documents.Count(d => d.Type == DocumentType.Outgoing)}\n\n";
            
            report += $"المسودات: {documents.Count(d => d.Status == DocumentStatus.Draft)}\n";
            report += $"المعالجة: {documents.Count(d => d.Status == DocumentStatus.Processed)}\n";
            report += $"المؤرشفة: {documents.Count(d => d.Status == DocumentStatus.Archived)}\n\n";
            
            var categoriesCount = documents.GroupBy(d => d.CategoryName)
                .Select(g => new { Category = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(5);
            
            report += "أكثر الفئات استخداماً:\n";
            foreach (var cat in categoriesCount)
            {
                report += $"- {cat.Category}: {cat.Count} وثيقة\n";
            }

            reportTextBox.Text = report;
        }

        private void GenerateDetailedReport(List<DocumentDto> documents, DateTime fromDate, DateTime toDate)
        {
            var report = $"تقرير مفصل للوثائق\n";
            report += $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            report += "قائمة الوثائق:\n";
            report += new string('-', 80) + "\n";
            
            foreach (var doc in documents.OrderByDescending(d => d.Date))
            {
                report += $"رقم الوثيقة: {doc.DocumentNumber}\n";
                report += $"العنوان: {doc.Title}\n";
                report += $"التاريخ: {doc.Date:yyyy/MM/dd}\n";
                report += $"النوع: {doc.TypeDisplayName}\n";
                report += $"المرسل/المستقبل: {doc.SenderRecipient}\n";
                report += $"الفئة: {doc.CategoryName}\n";
                report += $"الحالة: {doc.StatusDisplayName}\n";
                report += new string('-', 40) + "\n";
            }

            reportTextBox.Text = report;
        }

        private void GenerateCategoryReport(List<DocumentDto> documents, DateTime fromDate, DateTime toDate)
        {
            var report = $"تقرير الوثائق حسب الفئة\n";
            report += $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            var categoryGroups = documents.GroupBy(d => d.CategoryName)
                .Select(g => new { 
                    Category = g.Key, 
                    Count = g.Count(),
                    Incoming = g.Count(d => d.Type == DocumentType.Incoming),
                    Outgoing = g.Count(d => d.Type == DocumentType.Outgoing)
                })
                .OrderByDescending(x => x.Count);
            
            foreach (var group in categoryGroups)
            {
                report += $"الفئة: {group.Category}\n";
                report += $"إجمالي الوثائق: {group.Count}\n";
                report += $"الواردة: {group.Incoming}\n";
                report += $"الصادرة: {group.Outgoing}\n";
                report += new string('-', 40) + "\n";
            }

            reportTextBox.Text = report;
        }

        private void GenerateMonthlyReport(List<DocumentDto> documents, DateTime fromDate, DateTime toDate)
        {
            var report = $"تقرير الوثائق الشهري\n";
            report += $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            var monthlyGroups = documents.GroupBy(d => new { d.Date.Year, d.Date.Month })
                .Select(g => new {
                    Month = $"{g.Key.Year}/{g.Key.Month:D2}",
                    Count = g.Count(),
                    Incoming = g.Count(d => d.Type == DocumentType.Incoming),
                    Outgoing = g.Count(d => d.Type == DocumentType.Outgoing)
                })
                .OrderBy(x => x.Month);
            
            foreach (var group in monthlyGroups)
            {
                report += $"الشهر: {group.Month}\n";
                report += $"إجمالي الوثائق: {group.Count}\n";
                report += $"الواردة: {group.Incoming}\n";
                report += $"الصادرة: {group.Outgoing}\n";
                report += new string('-', 40) + "\n";
            }

            reportTextBox.Text = report;
        }

        private void GenerateStatusReport(List<DocumentDto> documents, DateTime fromDate, DateTime toDate)
        {
            var report = $"تقرير الوثائق حسب الحالة\n";
            report += $"الفترة: من {fromDate:yyyy/MM/dd} إلى {toDate:yyyy/MM/dd}\n";
            report += $"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}\n\n";
            
            var statusGroups = documents.GroupBy(d => d.Status)
                .Select(g => new {
                    Status = g.Key,
                    StatusName = Business.Helpers.NumberingHelper.GetDocumentStatusDisplayName(g.Key),
                    Count = g.Count(),
                    Incoming = g.Count(d => d.Type == DocumentType.Incoming),
                    Outgoing = g.Count(d => d.Type == DocumentType.Outgoing)
                })
                .OrderBy(x => x.Status);
            
            foreach (var group in statusGroups)
            {
                report += $"الحالة: {group.StatusName}\n";
                report += $"إجمالي الوثائق: {group.Count}\n";
                report += $"الواردة: {group.Incoming}\n";
                report += $"الصادرة: {group.Outgoing}\n";
                report += new string('-', 40) + "\n";
            }

            reportTextBox.Text = report;
        }

        private async void refreshButton_Click(object sender, EventArgs e)
        {
            await LoadStatisticsAsync();
            await LoadCategoryStatisticsAsync();
            await LoadMonthlyStatisticsAsync();
        }

        private async void exportExcelButton_Click(object sender, EventArgs e)
        {
            await ExportToExcelAsync();
        }

        private async void exportPdfButton_Click(object sender, EventArgs e)
        {
            await ExportToPdfAsync();
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            Close();
        }

        private async Task ExportToExcelAsync()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_الإحصائيات_{DateTime.Now:yyyy-MM-dd}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var loadingForm = MessageHelper.ShowLoadingMessage("جاري تصدير التقرير إلى Excel...");

                    try
                    {
                        var categories = await _categoryService.GetAllAsync();
                        var excelData = await _exportService.ExportStatisticsToExcelAsync(_currentStatistics, "تقرير الإحصائيات");

                        await Task.Run(() => File.WriteAllBytes(saveDialog.FileName, excelData));

                        MessageHelper.ShowSuccess($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}");

                        if (MessageHelper.ShowQuestion("هل تريد فتح الملف الآن؟"))
                        {
                            System.Diagnostics.Process.Start(saveDialog.FileName);
                        }
                    }
                    finally
                    {
                        MessageHelper.CloseLoadingMessage(loadingForm);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تصدير التقرير إلى Excel");
            }
        }

        private async Task ExportToPdfAsync()
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_الإحصائيات_{DateTime.Now:yyyy-MM-dd}.pdf"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    var loadingForm = MessageHelper.ShowLoadingMessage("جاري تصدير التقرير إلى PDF...");

                    try
                    {
                        var categories = await _categoryService.GetAllAsync();
                        var pdfData = await _exportService.GeneratePdfStatisticsReportAsync(_currentStatistics, categories, "تقرير الإحصائيات");

                        await Task.Run(() => File.WriteAllBytes(saveDialog.FileName, pdfData));

                        MessageHelper.ShowSuccess($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}");

                        if (MessageHelper.ShowQuestion("هل تريد فتح الملف الآن؟"))
                        {
                            System.Diagnostics.Process.Start(saveDialog.FileName);
                        }
                    }
                    finally
                    {
                        MessageHelper.CloseLoadingMessage(loadingForm);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageHelper.ShowException(ex, "فشل في تصدير التقرير إلى PDF");
            }
        }
    }
}
