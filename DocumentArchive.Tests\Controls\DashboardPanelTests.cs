using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using DocumentArchive.UI.Controls;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace DocumentArchive.Tests.Controls
{
    [TestClass]
    public class DashboardPanelTests
    {
        private Mock<IDocumentService> _mockDocumentService;
        private Mock<ICategoryService> _mockCategoryService;
        private DashboardPanel _dashboardPanel;

        [TestInitialize]
        public void Setup()
        {
            _mockDocumentService = new Mock<IDocumentService>();
            _mockCategoryService = new Mock<ICategoryService>();
            
            SetupMockData();
            
            _dashboardPanel = new DashboardPanel(_mockDocumentService.Object, _mockCategoryService.Object);
        }

        private void SetupMockData()
        {
            // Setup statistics
            var statistics = new Dictionary<string, int>
            {
                { "TotalDocuments", 100 },
                { "IncomingDocuments", 60 },
                { "OutgoingDocuments", 40 },
                { "DraftDocuments", 10 },
                { "ProcessedDocuments", 70 },
                { "ArchivedDocuments", 20 }
            };

            _mockDocumentService.Setup(s => s.GetStatisticsAsync())
                .ReturnsAsync(statistics);

            // Setup recent documents
            var recentDocuments = new PagedResult<DocumentDto>
            {
                Items = new List<DocumentDto>
                {
                    new DocumentDto
                    {
                        Id = 1,
                        DocumentNumber = "IN-2024-0001",
                        Title = "وثيقة حديثة 1",
                        Date = DateTime.Now.AddDays(-1),
                        Type = DocumentType.Incoming,
                        Status = DocumentStatus.Processed,
                        TypeDisplayName = "وارد",
                        StatusDisplayName = "معالج",
                        CreatedDate = DateTime.Now.AddDays(-1)
                    },
                    new DocumentDto
                    {
                        Id = 2,
                        DocumentNumber = "OUT-2024-0001",
                        Title = "وثيقة حديثة 2",
                        Date = DateTime.Now.AddDays(-2),
                        Type = DocumentType.Outgoing,
                        Status = DocumentStatus.Draft,
                        TypeDisplayName = "صادر",
                        StatusDisplayName = "مسودة",
                        CreatedDate = DateTime.Now.AddDays(-2)
                    }
                },
                TotalCount = 2,
                PageNumber = 1,
                PageSize = 10,
                TotalPages = 1
            };

            _mockDocumentService.Setup(s => s.GetAllAsync(It.IsAny<SearchCriteriaDto>()))
                .ReturnsAsync(recentDocuments);

            // Setup categories
            var categories = new List<CategoryDto>
            {
                new CategoryDto
                {
                    Id = 1,
                    Name = "عقود",
                    Description = "وثائق العقود",
                    IsActive = true,
                    DocumentCount = 25,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    ModifiedDate = DateTime.Now.AddDays(-10)
                },
                new CategoryDto
                {
                    Id = 2,
                    Name = "فواتير",
                    Description = "الفواتير والمدفوعات",
                    IsActive = true,
                    DocumentCount = 15,
                    CreatedDate = DateTime.Now.AddDays(-25),
                    ModifiedDate = DateTime.Now.AddDays(-5)
                }
            };

            _mockCategoryService.Setup(s => s.GetAllAsync())
                .ReturnsAsync(categories);
        }

        [TestMethod]
        public void Constructor_WithValidServices_ShouldCreateInstance()
        {
            // Act & Assert
            Assert.IsNotNull(_dashboardPanel);
            Assert.IsInstanceOfType(_dashboardPanel, typeof(UserControl));
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_WithNullDocumentService_ShouldThrowException()
        {
            // Act
            new DashboardPanel(null, _mockCategoryService.Object);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_WithNullCategoryService_ShouldThrowException()
        {
            // Act
            new DashboardPanel(_mockDocumentService.Object, null);
        }

        [TestMethod]
        public async Task LoadDashboardAsync_ShouldCallAllServices()
        {
            // Act
            await _dashboardPanel.LoadDashboardAsync();

            // Assert
            _mockDocumentService.Verify(s => s.GetStatisticsAsync(), Times.AtLeastOnce);
            _mockDocumentService.Verify(s => s.GetAllAsync(It.IsAny<SearchCriteriaDto>()), Times.AtLeastOnce);
            _mockCategoryService.Verify(s => s.GetAllAsync(), Times.AtLeastOnce);
        }

        [TestMethod]
        public async Task LoadDashboardAsync_WithServiceException_ShouldHandleGracefully()
        {
            // Arrange
            _mockDocumentService.Setup(s => s.GetStatisticsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert - Should not throw
            await _dashboardPanel.LoadDashboardAsync();
        }

        [TestMethod]
        public void DocumentSelected_Event_ShouldBeRaised()
        {
            // Arrange
            var eventRaised = false;
            var documentId = 0;

            _dashboardPanel.DocumentSelected += (sender, args) =>
            {
                eventRaised = true;
                documentId = args.DocumentId;
            };

            // Act
            var eventArgs = new DocumentSelectedEventArgs(123);
            _dashboardPanel.DocumentSelected?.Invoke(_dashboardPanel, eventArgs);

            // Assert
            Assert.IsTrue(eventRaised);
            Assert.AreEqual(123, documentId);
        }

        [TestMethod]
        public void StatisticsCard_UpdateValue_ShouldUpdateDisplay()
        {
            // Arrange
            var card = new StatisticsCard("إجمالي الوثائق", "📄", System.Drawing.Color.Blue);

            // Act
            card.UpdateValue(150);

            // Assert
            Assert.IsNotNull(card);
            // Note: In a real test, we would check the label text, but that requires UI testing framework
        }

        [TestMethod]
        public void StatisticsCard_UpdateValueWithString_ShouldUpdateDisplay()
        {
            // Arrange
            var card = new StatisticsCard("الحالة", "📊", System.Drawing.Color.Green);

            // Act
            card.UpdateValue("نشط");

            // Assert
            Assert.IsNotNull(card);
        }

        [TestMethod]
        public void DocumentSelectedEventArgs_Constructor_ShouldSetDocumentId()
        {
            // Arrange
            var documentId = 456;

            // Act
            var eventArgs = new DocumentSelectedEventArgs(documentId);

            // Assert
            Assert.AreEqual(documentId, eventArgs.DocumentId);
        }

        [TestMethod]
        public async Task LoadDashboardAsync_WithEmptyStatistics_ShouldHandleGracefully()
        {
            // Arrange
            _mockDocumentService.Setup(s => s.GetStatisticsAsync())
                .ReturnsAsync(new Dictionary<string, int>());

            // Act & Assert - Should not throw
            await _dashboardPanel.LoadDashboardAsync();
        }

        [TestMethod]
        public async Task LoadDashboardAsync_WithEmptyDocuments_ShouldHandleGracefully()
        {
            // Arrange
            var emptyResult = new PagedResult<DocumentDto>
            {
                Items = new List<DocumentDto>(),
                TotalCount = 0,
                PageNumber = 1,
                PageSize = 10,
                TotalPages = 0
            };

            _mockDocumentService.Setup(s => s.GetAllAsync(It.IsAny<SearchCriteriaDto>()))
                .ReturnsAsync(emptyResult);

            // Act & Assert - Should not throw
            await _dashboardPanel.LoadDashboardAsync();
        }

        [TestMethod]
        public async Task LoadDashboardAsync_WithEmptyCategories_ShouldHandleGracefully()
        {
            // Arrange
            _mockCategoryService.Setup(s => s.GetAllAsync())
                .ReturnsAsync(new List<CategoryDto>());

            // Act & Assert - Should not throw
            await _dashboardPanel.LoadDashboardAsync();
        }

        [TestMethod]
        public void DashboardPanel_Properties_ShouldBeConfiguredForArabic()
        {
            // Assert
            Assert.AreEqual(RightToLeft.Yes, _dashboardPanel.RightToLeft);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _dashboardPanel?.Dispose();
            _mockDocumentService = null;
            _mockCategoryService = null;
        }
    }
}
