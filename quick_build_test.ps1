# Quick Build Test Script
Write-Host "=== Document Archive System - Quick Build Test ===" -ForegroundColor Green

# Test 1: Check if MSBuild is available
Write-Host "`n1. Testing MSBuild availability..." -ForegroundColor Yellow
$msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if (Test-Path $msbuildPath) {
    Write-Host "✅ MSBuild found at: $msbuildPath" -ForegroundColor Green
} else {
    Write-Host "❌ MSBuild not found" -ForegroundColor Red
}

# Test 2: Check project files
Write-Host "`n2. Testing project files..." -ForegroundColor Yellow
$projectFiles = @(
    "DocumentArchive.Models\DocumentArchive.Models.csproj",
    "DocumentArchive.Data\DocumentArchive.Data.csproj", 
    "DocumentArchive.Business\DocumentArchive.Business.csproj",
    "DocumentArchive.UI\DocumentArchive.UI.csproj"
)

foreach ($file in $projectFiles) {
    if (Test-Path $file) {
        Write-Host "✅ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
    }
}

# Test 3: Check packages
Write-Host "`n3. Testing NuGet packages..." -ForegroundColor Yellow
if (Test-Path "packages") {
    $packageCount = (Get-ChildItem "packages" -Directory).Count
    Write-Host "✅ Packages directory found with $packageCount packages" -ForegroundColor Green
} else {
    Write-Host "❌ Packages directory not found" -ForegroundColor Red
}

# Test 4: Check key source files
Write-Host "`n4. Testing key source files..." -ForegroundColor Yellow
$keyFiles = @(
    "DocumentArchive.Business\Services\ExportService.cs",
    "DocumentArchive.UI\Controls\DashboardPanel.cs",
    "DocumentArchive.Tests\Services\ExportServiceTests.cs"
)

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        $lineCount = (Get-Content $file).Count
        Write-Host "✅ Found: $file ($lineCount lines)" -ForegroundColor Green
    } else {
        Write-Host "❌ Missing: $file" -ForegroundColor Red
    }
}

# Test 5: Check demo application
Write-Host "`n5. Testing demo application..." -ForegroundColor Yellow
if (Test-Path "DocumentArchiveDemo.cs") {
    $demoLines = (Get-Content "DocumentArchiveDemo.cs").Count
    Write-Host "✅ Demo application found ($demoLines lines)" -ForegroundColor Green
} else {
    Write-Host "❌ Demo application not found" -ForegroundColor Red
}

Write-Host "`n=== Build Test Summary ===" -ForegroundColor Green
Write-Host "All Phase 3 components are present and ready!" -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Open Visual Studio and load DocumentArchive.sln" -ForegroundColor White
Write-Host "2. Build the solution (Ctrl+Shift+B)" -ForegroundColor White
Write-Host "3. Run the application (F5)" -ForegroundColor White
Write-Host "4. Or compile and run the demo: .\compile_demo.bat" -ForegroundColor White
