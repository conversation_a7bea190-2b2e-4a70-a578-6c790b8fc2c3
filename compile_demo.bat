@echo off
echo ========================================
echo Document Archive System - Demo Compiler
echo ========================================

echo Compiling demo application...

"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" ^
    /target:winexe ^
    /out:DocumentArchiveDemo.exe ^
    /reference:System.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Drawing.dll ^
    DocumentArchiveDemo.cs

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Demo compiled successfully!
    echo.
    echo Starting demo application...
    start DocumentArchiveDemo.exe
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please check the error messages above.
)

pause
