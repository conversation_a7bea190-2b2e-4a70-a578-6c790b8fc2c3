using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using DocumentArchive.Models.Enums;

namespace DocumentArchive.Models.Entities
{
    /// <summary>
    /// Represents a document in the archive system
    /// </summary>
    [Table("Documents")]
    public class Document
    {
        /// <summary>
        /// Unique identifier for the document
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// Title of the document
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        /// <summary>
        /// Auto-generated document number (e.g., IN-2024-0001, OUT-2024-0001)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string DocumentNumber { get; set; }

        /// <summary>
        /// Date of the document
        /// </summary>
        [Required]
        public DateTime Date { get; set; }

        /// <summary>
        /// Type of document (Incoming/Outgoing)
        /// </summary>
        [Required]
        public DocumentType Type { get; set; }

        /// <summary>
        /// Sender (for incoming) or Recipient (for outgoing)
        /// </summary>
        [Required]
        [StringLength(200)]
        public string SenderRecipient { get; set; }

        /// <summary>
        /// Foreign key to Category
        /// </summary>
        [Required]
        public int CategoryId { get; set; }

        /// <summary>
        /// Description or notes about the document
        /// </summary>
        [StringLength(1000)]
        public string Description { get; set; }

        /// <summary>
        /// Current status of the document
        /// </summary>
        [Required]
        public DocumentStatus Status { get; set; } = DocumentStatus.Draft;

        /// <summary>
        /// Physical file path on disk
        /// </summary>
        [StringLength(500)]
        public string FilePath { get; set; }

        /// <summary>
        /// Original filename when uploaded
        /// </summary>
        [StringLength(255)]
        public string OriginalFileName { get; set; }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// MIME type of the file
        /// </summary>
        [StringLength(100)]
        public string MimeType { get; set; }

        /// <summary>
        /// Date when the document was created in the system
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Date when the document was last modified
        /// </summary>
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// Navigation property to Category
        /// </summary>
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }
    }
}
