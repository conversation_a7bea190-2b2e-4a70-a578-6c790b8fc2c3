using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DocumentArchive.Business.Services;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DocumentArchive.Tests.Services
{
    [TestClass]
    public class ExportServiceTests
    {
        private ExportService _exportService;
        private List<DocumentDto> _testDocuments;
        private List<CategoryDto> _testCategories;
        private Dictionary<string, int> _testStatistics;

        [TestInitialize]
        public void Setup()
        {
            _exportService = new ExportService();
            SetupTestData();
        }

        private void SetupTestData()
        {
            _testDocuments = new List<DocumentDto>
            {
                new DocumentDto
                {
                    Id = 1,
                    DocumentNumber = "IN-2024-0001",
                    Title = "وثيقة تجريبية 1",
                    Date = DateTime.Now.AddDays(-5),
                    Type = DocumentType.Incoming,
                    Status = DocumentStatus.Processed,
                    SenderRecipient = "المرسل الأول",
                    CategoryName = "عقود",
                    Description = "وصف الوثيقة الأولى",
                    CreatedDate = DateTime.Now.AddDays(-5),
                    ModifiedDate = DateTime.Now.AddDays(-2),
                    TypeDisplayName = "وارد",
                    StatusDisplayName = "معالج"
                },
                new DocumentDto
                {
                    Id = 2,
                    DocumentNumber = "OUT-2024-0001",
                    Title = "وثيقة تجريبية 2",
                    Date = DateTime.Now.AddDays(-3),
                    Type = DocumentType.Outgoing,
                    Status = DocumentStatus.Draft,
                    SenderRecipient = "المستقبل الأول",
                    CategoryName = "فواتير",
                    Description = "وصف الوثيقة الثانية",
                    CreatedDate = DateTime.Now.AddDays(-3),
                    ModifiedDate = DateTime.Now.AddDays(-1),
                    TypeDisplayName = "صادر",
                    StatusDisplayName = "مسودة"
                }
            };

            _testCategories = new List<CategoryDto>
            {
                new CategoryDto
                {
                    Id = 1,
                    Name = "عقود",
                    Description = "وثائق العقود والاتفاقيات",
                    IsActive = true,
                    DocumentCount = 5,
                    CreatedDate = DateTime.Now.AddDays(-30),
                    ModifiedDate = DateTime.Now.AddDays(-10)
                },
                new CategoryDto
                {
                    Id = 2,
                    Name = "فواتير",
                    Description = "الفواتير والمدفوعات",
                    IsActive = true,
                    DocumentCount = 3,
                    CreatedDate = DateTime.Now.AddDays(-25),
                    ModifiedDate = DateTime.Now.AddDays(-5)
                }
            };

            _testStatistics = new Dictionary<string, int>
            {
                { "TotalDocuments", 10 },
                { "IncomingDocuments", 6 },
                { "OutgoingDocuments", 4 },
                { "DraftDocuments", 2 },
                { "ProcessedDocuments", 5 },
                { "ArchivedDocuments", 3 }
            };
        }

        [TestMethod]
        public async Task ExportDocumentsToExcelAsync_ShouldReturnValidExcelData()
        {
            // Act
            var result = await _exportService.ExportDocumentsToExcelAsync(_testDocuments, "تقرير الوثائق");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid Excel file (starts with PK for ZIP format)
            Assert.IsTrue(result[0] == 0x50 && result[1] == 0x4B);
        }

        [TestMethod]
        public async Task ExportCategoriesToExcelAsync_ShouldReturnValidExcelData()
        {
            // Act
            var result = await _exportService.ExportCategoriesToExcelAsync(_testCategories, "تقرير الفئات");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid Excel file
            Assert.IsTrue(result[0] == 0x50 && result[1] == 0x4B);
        }

        [TestMethod]
        public async Task ExportStatisticsToExcelAsync_ShouldReturnValidExcelData()
        {
            // Act
            var result = await _exportService.ExportStatisticsToExcelAsync(_testStatistics, "تقرير الإحصائيات");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid Excel file
            Assert.IsTrue(result[0] == 0x50 && result[1] == 0x4B);
        }

        [TestMethod]
        public async Task ExportDocumentsToCsvAsync_ShouldReturnValidCsvData()
        {
            // Act
            var result = await _exportService.ExportDocumentsToCsvAsync(_testDocuments);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            var csvContent = System.Text.Encoding.UTF8.GetString(result);
            Assert.IsTrue(csvContent.Contains("رقم الوثيقة"));
            Assert.IsTrue(csvContent.Contains("IN-2024-0001"));
            Assert.IsTrue(csvContent.Contains("OUT-2024-0001"));
        }

        [TestMethod]
        public async Task GeneratePdfReportAsync_ShouldReturnValidPdfData()
        {
            // Act
            var result = await _exportService.GeneratePdfReportAsync(_testDocuments, "تقرير الوثائق", "summary");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid PDF file (starts with %PDF)
            var pdfHeader = System.Text.Encoding.ASCII.GetString(result.Take(4).ToArray());
            Assert.AreEqual("%PDF", pdfHeader);
        }

        [TestMethod]
        public async Task GeneratePdfStatisticsReportAsync_ShouldReturnValidPdfData()
        {
            // Act
            var result = await _exportService.GeneratePdfStatisticsReportAsync(_testStatistics, _testCategories, "تقرير الإحصائيات");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid PDF file
            var pdfHeader = System.Text.Encoding.ASCII.GetString(result.Take(4).ToArray());
            Assert.AreEqual("%PDF", pdfHeader);
        }

        [TestMethod]
        public async Task ExportSearchResultsToExcelAsync_ShouldReturnValidExcelData()
        {
            // Arrange
            var criteria = new SearchCriteriaDto
            {
                SearchText = "تجريبية",
                DateFrom = DateTime.Now.AddDays(-10),
                DateTo = DateTime.Now,
                DocumentType = DocumentType.Incoming
            };

            // Act
            var result = await _exportService.ExportSearchResultsToExcelAsync(criteria, _testDocuments);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Check if it's a valid Excel file
            Assert.IsTrue(result[0] == 0x50 && result[1] == 0x4B);
        }

        [TestMethod]
        public void GetSupportedFormats_ShouldReturnExpectedFormats()
        {
            // Act
            var formats = _exportService.GetSupportedFormats();

            // Assert
            Assert.IsNotNull(formats);
            Assert.IsTrue(formats.Contains("Excel"));
            Assert.IsTrue(formats.Contains("CSV"));
            Assert.IsTrue(formats.Contains("PDF"));
            Assert.IsTrue(formats.Contains("Text"));
        }

        [TestMethod]
        public void GetFileExtension_ShouldReturnCorrectExtensions()
        {
            // Act & Assert
            Assert.AreEqual(".xlsx", _exportService.GetFileExtension("Excel"));
            Assert.AreEqual(".csv", _exportService.GetFileExtension("CSV"));
            Assert.AreEqual(".pdf", _exportService.GetFileExtension("PDF"));
            Assert.AreEqual(".txt", _exportService.GetFileExtension("Text"));
            Assert.AreEqual(".txt", _exportService.GetFileExtension("Unknown"));
        }

        [TestMethod]
        public void GetMimeType_ShouldReturnCorrectMimeTypes()
        {
            // Act & Assert
            Assert.AreEqual("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                _exportService.GetMimeType("Excel"));
            Assert.AreEqual("text/csv", _exportService.GetMimeType("CSV"));
            Assert.AreEqual("application/pdf", _exportService.GetMimeType("PDF"));
            Assert.AreEqual("text/plain", _exportService.GetMimeType("Text"));
            Assert.AreEqual("text/plain", _exportService.GetMimeType("Unknown"));
        }

        [TestMethod]
        public async Task ExportDocumentsToExcelAsync_WithEmptyList_ShouldReturnValidExcelData()
        {
            // Arrange
            var emptyDocuments = new List<DocumentDto>();

            // Act
            var result = await _exportService.ExportDocumentsToExcelAsync(emptyDocuments, "تقرير فارغ");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
            
            // Should still be a valid Excel file even with no data
            Assert.IsTrue(result[0] == 0x50 && result[1] == 0x4B);
        }

        [TestMethod]
        public async Task ExportDocumentsToExcelAsync_WithNullTitle_ShouldUseDefaultTitle()
        {
            // Act
            var result = await _exportService.ExportDocumentsToExcelAsync(_testDocuments, null);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsTrue(result.Length > 0);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _exportService = null;
            _testDocuments = null;
            _testCategories = null;
            _testStatistics = null;
        }
    }
}
