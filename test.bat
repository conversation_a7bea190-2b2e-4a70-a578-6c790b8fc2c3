@echo off
echo ========================================
echo تشغيل اختبارات نظام أرشفة الوثائق
echo Running Document Archive System Tests
echo ========================================

echo.
echo [1/4] جاري بناء المشروع...
echo [1/4] Building Test project...
msbuild DocumentArchive.Tests\DocumentArchive.Tests.csproj /p:Configuration=Debug /p:Platform="Any CPU"
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo ERROR: Failed to build Test project
    pause
    exit /b 1
)

echo.
echo [2/4] تشغيل اختبارات الخدمات...
echo [2/4] Running Service Tests...
vstest.console.exe DocumentArchive.Tests\bin\Debug\DocumentArchive.Tests.dll /TestCaseFilter:"TestCategory=Services"

echo.
echo [3/4] تشغيل اختبارات المساعدات والتحكمات...
echo [3/4] Running Helper and Control Tests...
vstest.console.exe DocumentArchive.Tests\bin\Debug\DocumentArchive.Tests.dll /TestCaseFilter:"TestCategory=Helpers|TestCategory=Controls"

echo.
echo [4/4] تشغيل جميع الاختبارات...
echo [4/4] Running All Tests...
vstest.console.exe DocumentArchive.Tests\bin\Debug\DocumentArchive.Tests.dll
if %errorlevel% neq 0 (
    echo ⚠️ فشلت بعض الاختبارات
    echo WARNING: Some tests failed
) else (
    echo ✅ تم تشغيل جميع الاختبارات بنجاح
    echo ✅ All tests passed successfully
)

echo.
echo 📊 انتهاء تشغيل الاختبارات
echo 📊 Test run completed
echo ========================================
pause
