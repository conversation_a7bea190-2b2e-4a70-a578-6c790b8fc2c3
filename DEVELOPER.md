# دليل المطور - نظام أرشفة الوثائق
## Developer Guide - Document Archive System

---

## 🏗️ **هيكل المشروع**

### المشاريع الفرعية:
```
DocumentArchive.sln
├── DocumentArchive.Models      # النماذج والكيانات
├── DocumentArchive.Data        # طبقة الوصول للبيانات
├── DocumentArchive.Business    # منطق الأعمال والخدمات
├── DocumentArchive.UI          # واجهة المستخدم (Windows Forms)
└── DocumentArchive.Tests       # الاختبارات
```

### التقنيات المستخدمة:
- **.NET Framework 4.8**
- **Windows Forms** للواجهة
- **Entity Framework 6.x** مع SQLite
- **EPPlus 4.5.3.3** لتصدير Excel
- **iTextSharp 5.5.13.3** لتصدير PDF
- **MSTest** للاختبارات
- **Moq** للـ Mock Objects

---

## 🔧 **إعداد بيئة التطوير**

### المتطلبات:
1. **Visual Studio 2019/2022**
2. **.NET Framework 4.8 Developer Pack**
3. **NuGet Package Manager**

### خطوات الإعداد:
```bash
# 1. استنساخ المستودع
git clone [repository-url]
cd DocumentArchive

# 2. تشغيل الإعداد التلقائي
setup-dev.bat

# 3. فتح الحل في Visual Studio
start DocumentArchive.sln
```

---

## 📦 **إدارة الحزم**

### الحزم الرئيسية:
```xml
<!-- DocumentArchive.Business -->
<package id="EPPlus" version="4.5.3.3" />
<package id="iTextSharp" version="5.5.13.3" />
<package id="NLog" version="4.7.15" />

<!-- DocumentArchive.Data -->
<package id="EntityFramework" version="6.4.4" />
<package id="System.Data.SQLite" version="1.0.118" />

<!-- DocumentArchive.Tests -->
<package id="MSTest.TestFramework" version="2.2.10" />
<package id="Moq" version="4.20.69" />
```

### إضافة حزمة جديدة:
```bash
# من Package Manager Console
Install-Package PackageName -Project ProjectName

# أو من NuGet UI
Tools → NuGet Package Manager → Manage NuGet Packages for Solution
```

---

## 🏛️ **معمارية النظام**

### طبقة النماذج (Models):
```csharp
// الكيانات الأساسية
public class Document { }
public class Category { }

// DTOs للنقل
public class DocumentDto { }
public class CategoryDto { }

// معايير البحث
public class SearchCriteriaDto { }
```

### طبقة البيانات (Data):
```csharp
// Repository Pattern
public interface IDocumentRepository { }
public class DocumentRepository : IDocumentRepository { }

// Unit of Work
public interface IUnitOfWork { }
public class UnitOfWork : IUnitOfWork { }
```

### طبقة الأعمال (Business):
```csharp
// الخدمات
public interface IDocumentService { }
public class DocumentService : IDocumentService { }

// خدمة التصدير الجديدة
public interface IExportService { }
public class ExportService : IExportService { }
```

### طبقة الواجهة (UI):
```csharp
// النماذج الرئيسية
public partial class MainForm : Form { }
public partial class DocumentForm : Form { }
public partial class ReportsForm : Form { }

// التحكمات المخصصة
public partial class DashboardPanel : UserControl { }
```

---

## 🧪 **كتابة الاختبارات**

### هيكل الاختبارات:
```csharp
[TestClass]
public class ExportServiceTests
{
    private ExportService _exportService;
    private List<DocumentDto> _testData;

    [TestInitialize]
    public void Setup()
    {
        _exportService = new ExportService();
        SetupTestData();
    }

    [TestMethod]
    public async Task ExportToExcel_ShouldReturnValidData()
    {
        // Arrange
        var documents = _testData;

        // Act
        var result = await _exportService.ExportDocumentsToExcelAsync(documents);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Length > 0);
    }
}
```

### تشغيل الاختبارات:
```bash
# من سطر الأوامر
test.bat

# من Visual Studio
Test → Run All Tests (Ctrl+R, A)
```

---

## 📊 **خدمة التصدير المتقدمة**

### إضافة تنسيق تصدير جديد:
```csharp
public async Task<byte[]> ExportToNewFormatAsync(List<DocumentDto> documents)
{
    try
    {
        // تنفيذ التصدير
        return await CreateNewFormatData(documents);
    }
    catch (Exception ex)
    {
        Logger.Error(ex, "Error exporting to new format");
        throw;
    }
}
```

### تخصيص تصدير Excel:
```csharp
private byte[] CreateCustomExcel(List<DocumentDto> documents)
{
    using (var package = new ExcelPackage())
    {
        var worksheet = package.Workbook.Worksheets.Add("البيانات");
        
        // تعيين RTL للعربية
        worksheet.View.RightToLeft = true;
        
        // إضافة البيانات والتنسيق
        // ...
        
        return package.GetAsByteArray();
    }
}
```

---

## 🎨 **تطوير الواجهة**

### إضافة نموذج جديد:
```csharp
public partial class NewForm : Form
{
    public NewForm()
    {
        InitializeComponent();
        InitializeForm();
    }

    private void InitializeForm()
    {
        // تكوين النموذج للعربية
        UIHelper.ConfigureFormForArabic(this);
        
        // تطبيق الأنماط
        UIHelper.StylePrimaryButton(saveButton);
        UIHelper.StyleSecondaryButton(cancelButton);
    }
}
```

### إضافة تحكم مخصص:
```csharp
public partial class CustomControl : UserControl
{
    public CustomControl()
    {
        InitializeComponent();
        ConfigureForArabic();
    }

    private void ConfigureForArabic()
    {
        RightToLeft = RightToLeft.Yes;
        Font = new Font("Tahoma", 9F);
    }
}
```

---

## 🔍 **تطوير البحث المتقدم**

### إضافة معيار بحث جديد:
```csharp
public class SearchCriteriaDto
{
    // المعايير الموجودة
    public string SearchText { get; set; }
    public DateTime? DateFrom { get; set; }
    
    // معيار جديد
    public string NewCriteria { get; set; }
}
```

### تحديث خدمة البحث:
```csharp
public async Task<PagedResult<DocumentDto>> SearchAsync(SearchCriteriaDto criteria)
{
    var query = _context.Documents.AsQueryable();
    
    // تطبيق المعايير الموجودة
    if (!string.IsNullOrEmpty(criteria.SearchText))
        query = query.Where(d => d.Title.Contains(criteria.SearchText));
    
    // تطبيق المعيار الجديد
    if (!string.IsNullOrEmpty(criteria.NewCriteria))
        query = query.Where(d => d.NewField.Contains(criteria.NewCriteria));
    
    return await ExecutePagedQuery(query, criteria);
}
```

---

## 📈 **تطوير لوحة المعلومات**

### إضافة إحصائية جديدة:
```csharp
private async Task LoadNewStatisticAsync()
{
    try
    {
        var newStat = await _service.GetNewStatisticAsync();
        newStatLabel.Text = newStat.ToString();
    }
    catch (Exception ex)
    {
        Logger.Error(ex, "Error loading new statistic");
    }
}
```

### إضافة رسم بياني:
```csharp
private void CreateChart(Dictionary<string, int> data)
{
    // استخدام مكتبة رسوم بيانية مثل Chart.js أو DevExpress
    var chart = new Chart();
    // تكوين الرسم البياني
}
```

---

## 🐛 **تتبع الأخطاء**

### استخدام NLog:
```csharp
private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

public async Task SomeMethodAsync()
{
    try
    {
        Logger.Info("Starting operation");
        // العملية
        Logger.Info("Operation completed successfully");
    }
    catch (Exception ex)
    {
        Logger.Error(ex, "Operation failed");
        throw;
    }
}
```

### تكوين NLog:
```xml
<!-- NLog.config -->
<nlog>
  <targets>
    <target name="file" type="File" 
            fileName="Logs/DocumentArchive-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />
  </targets>
  <rules>
    <logger name="*" minlevel="Info" writeTo="file" />
  </rules>
</nlog>
```

---

## 🚀 **النشر والتوزيع**

### بناء الإصدار:
```bash
# بناء Release
msbuild DocumentArchive.sln /p:Configuration=Release /p:Platform="Any CPU"

# إنشاء حزمة التثبيت
# استخدم WiX أو Inno Setup
```

### ملفات النشر المطلوبة:
- **DocumentArchive.exe** (الملف الرئيسي)
- **جميع DLL المطلوبة**
- **ملفات التكوين** (App.config, NLog.config)
- **مجلدات النظام** (Data, Documents, Logs)

---

## 📝 **أفضل الممارسات**

### الكود:
1. **استخدم async/await** للعمليات الطويلة
2. **طبق Repository Pattern** لطبقة البيانات
3. **استخدم Dependency Injection** للخدمات
4. **اكتب اختبارات شاملة** لكل ميزة جديدة

### الواجهة:
1. **استخدم UIHelper** للتنسيق الموحد
2. **طبق دعم العربية** في جميع النماذج
3. **استخدم MessageHelper** للرسائل
4. **اتبع نمط MVP/MVVM** حيث أمكن

### الأداء:
1. **استخدم Pagination** للقوائم الطويلة
2. **طبق Lazy Loading** للبيانات الكبيرة
3. **استخدم Caching** للبيانات المتكررة
4. **راقب استهلاك الذاكرة** في العمليات الكبيرة

---

## 🔄 **المراحل القادمة**

### Phase 4: System Enhancement
- [ ] النسخ الاحتياطي والاستعادة
- [ ] إعدادات النظام المتقدمة
- [ ] تحسينات الأداء والذاكرة
- [ ] دعم قواعد بيانات متعددة

### Phase 5: Final Polish
- [ ] اختبارات شاملة ومتكاملة
- [ ] توثيق المستخدم النهائي
- [ ] حزمة التثبيت والنشر
- [ ] دليل الصيانة والدعم

---

**آخر تحديث**: Phase 3 - يناير 2024
**للمساهمة**: راجع CONTRIBUTING.md (سيتم إنشاؤه لاحقاً)
