# نظام أرشفة الوثائق الإلكترونية
## Electronic Document Archive System

نظام شامل لإدارة وأرشفة الوثائق الإلكترونية الواردة والصادرة مع دعم اللغة العربية وتوافق مع Windows 7.

### المتطلبات التقنية

- **.NET Framework 4.8** (متوافق مع Windows 7)
- **Windows Forms** لواجهة المستخدم
- **Entity Framework 6.x** مع SQLite
- **Visual Studio 2019/2022**
- **Windows 7/8/10/11** (32-bit و 64-bit)

### هيكل المشروع

```
DocumentArchive.sln
├── DocumentArchive.UI          # طبقة واجهة المستخدم (Windows Forms)
├── DocumentArchive.Business    # طبقة منطق الأعمال
├── DocumentArchive.Data        # طبقة الوصول للبيانات
├── DocumentArchive.Models      # النماذج والكيانات
└── DocumentArchive.Tests       # اختبارات الوحدة
```

### الميزات الرئيسية

#### إدارة الوثائق
- ✅ إضافة وتعديل وحذف الوثائق
- ✅ تصنيف الوثائق (واردة/صادرة)
- ✅ نظام ترقيم تلقائي للوثائق
- ✅ إدارة حالات الوثائق (مسودة، معالج، مؤرشف)
- ✅ رفع وإدارة الملفات المرفقة

#### أنواع الملفات المدعومة
- PDF, DOC, DOCX
- XLS, XLSX
- JPG, JPEG, PNG
- TXT

#### البحث والتصفية
- 🔍 البحث النصي في العناوين والأوصاف
- 📅 التصفية حسب التاريخ
- 📂 التصفية حسب الفئة
- 📊 التصفية حسب الحالة
- 🔄 التصفية حسب النوع (وارد/صادر)

#### إدارة الفئات
- ✅ إنشاء وتعديل فئات مخصصة
- ✅ فئات افتراضية (عقود، فواتير، تقارير، إلخ)
- ✅ إحصائيات الفئات

#### الواجهة والتجربة
- 🌐 دعم كامل للغة العربية (RTL)
- 🎨 واجهة مستخدم حديثة ومتجاوبة
- 📱 تصميم متوافق مع معايير Windows
- ⚡ أداء محسن مع التحميل التدريجي

### التثبيت والإعداد

#### المتطلبات المسبقة
1. تثبيت .NET Framework 4.8 Developer Pack
2. تثبيت Visual Studio 2019 أو أحدث
3. تثبيت SQLite (مدمج مع المشروع)

#### خطوات التثبيت السريعة

**الطريقة الأولى: الإعداد التلقائي**
1. تشغيل ملف الإعداد:
```bash
setup-dev.bat
```

2. فتح الحل في Visual Studio:
```
DocumentArchive.sln
```

3. بناء وتشغيل:
```
F5 (أو Ctrl+F5)
```

**الطريقة الثانية: الإعداد اليدوي**
1. استنساخ المستودع:
```bash
git clone [repository-url]
cd DocumentArchive
```

2. بناء المشروع:
```bash
build.bat
```

3. تشغيل التطبيق:
```bash
DocumentArchive.UI\bin\Debug\DocumentArchive.exe
```

**الطريقة الثالثة: Visual Studio**
1. فتح `DocumentArchive.sln`
2. استعادة حزم NuGet: `Tools > NuGet Package Manager > Restore`
3. بناء الحل: `Build > Build Solution`
4. تشغيل: `Debug > Start Debugging (F5)`

### إعداد قاعدة البيانات

سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول في:
```
[Application Directory]\Data\DocumentArchive.db
```

### إعداد التخزين

سيتم إنشاء مجلد الوثائق تلقائياً في:
```
[Application Directory]\Documents\
├── 2024\
│   ├── 01\
│   │   ├── IN\     # الوثائق الواردة
│   │   └── OUT\    # الوثائق الصادرة
│   └── 02\
└── 2025\
```

### الاستخدام

#### إضافة وثيقة جديدة
1. انقر على "إضافة وثيقة" في شريط الأدوات
2. املأ البيانات المطلوبة (العنوان، التاريخ، المرسل/المستقبل)
3. اختر الفئة المناسبة
4. ارفق الملف (اختياري)
5. احفظ الوثيقة

#### البحث عن الوثائق
1. استخدم لوحة البحث الجانبية
2. أدخل النص المراد البحث عنه
3. حدد المرشحات (التاريخ، الفئة، الحالة)
4. انقر "بحث"

#### إدارة الفئات
1. اذهب إلى قائمة "أدوات" > "إدارة الفئات"
2. أضف أو عدل أو احذف الفئات حسب الحاجة

### التطوير والمساهمة

#### هيكل الكود
- **Models**: كيانات البيانات والـ DTOs
- **Data**: مستودعات البيانات وسياق قاعدة البيانات
- **Business**: خدمات منطق الأعمال والتحقق
- **UI**: نماذج Windows Forms والتحكمات

#### إضافة ميزة جديدة
1. أنشئ فرع جديد من `main`
2. أضف الكود في الطبقة المناسبة
3. أضف اختبارات الوحدة
4. اختبر الميزة بشكل شامل
5. أرسل طلب دمج

#### تشغيل الاختبارات
```bash
dotnet test DocumentArchive.Tests
```

### استكشاف الأخطاء

#### مشاكل شائعة

**خطأ في قاعدة البيانات:**
- تأكد من وجود مجلد `Data` في مجلد التطبيق
- تحقق من صلاحيات الكتابة في مجلد التطبيق

**مشاكل في رفع الملفات:**
- تأكد من وجود مجلد `Documents`
- تحقق من حجم الملف (الحد الأقصى 50 ميجابايت)
- تأكد من نوع الملف المدعوم

**مشاكل في العرض:**
- تأكد من تثبيت الخطوط العربية
- تحقق من إعدادات اللغة في Windows

### السجلات (Logs)

يتم حفظ السجلات في:
```
[Application Directory]\Logs\
├── DocumentArchive-2024-01-01.log
├── DocumentArchive-2024-01-02.log
└── Archive\
```

### الأمان والنسخ الاحتياطي

#### النسخ الاحتياطي
- انسخ مجلد `Data` لحفظ قاعدة البيانات
- انسخ مجلد `Documents` لحفظ الملفات

#### الأمان
- يتم تشفير كلمات المرور في قاعدة البيانات
- يتم التحقق من صحة الملفات عند الرفع
- حماية من SQL Injection

### الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح Issue في GitHub
- راجع ملف التوثيق التقني
- تحقق من السجلات للحصول على تفاصيل الأخطاء

### الترخيص

هذا المشروع مرخص تحت [اسم الترخيص] - راجع ملف LICENSE للتفاصيل.

### المساهمون

- [اسم المطور الرئيسي]
- [أسماء المساهمين الآخرين]

---

## 🚀 **حالة المشروع - Phase 3 مكتمل**

### ✅ **Phase 3: Advanced Features & Export Enhancement - مكتمل**

#### 🎯 **الميزات المتقدمة الجديدة**
- ✅ تصدير Excel حقيقي مع EPPlus
- ✅ تصدير PDF متقدم مع iTextSharp
- ✅ رسوم بيانية في التقارير
- ✅ تحسينات شاملة للتصدير
- ✅ اختبارات شاملة للميزات الجديدة

### ✅ **Phase 2: Database & Core Data Operations - مكتمل**

#### 🗄️ **قاعدة البيانات**
- ✅ إعداد SQLite مع Entity Framework 6.x
- ✅ تطبيق Code First Migrations
- ✅ بذر البيانات الأولية (فئات وعينات)
- ✅ فهرسة الجداول للأداء الأمثل

#### 🖥️ **واجهة المستخدم الأساسية**
- ✅ النموذج الرئيسي مع قائمة الوثائق
- ✅ لوحة البحث والتصفية المتقدمة
- ✅ نموذج إضافة/تعديل الوثائق
- ✅ نموذج إدارة الفئات
- ✅ دعم كامل للعربية (RTL)

#### ⚡ **الوظائف الأساسية**
- ✅ إضافة وتعديل وحذف الوثائق
- ✅ رفع وإدارة الملفات المرفقة
- ✅ البحث النصي والتصفية المتقدمة
- ✅ إدارة الفئات والحالات
- ✅ التنقل بين الصفحات (Pagination)

### 🔄 **المراحل التالية**

#### **Phase 4: System Enhancement**
- النسخ الاحتياطي والاستعادة
- إعدادات النظام المتقدمة
- تحسينات الأداء والذاكرة
- دعم قواعد بيانات متعددة

#### **Phase 5: Final Polish**
- اختبارات شاملة ومتكاملة
- توثيق المستخدم النهائي
- حزمة التثبيت والنشر
- دليل الصيانة والدعم

### 🎯 **كيفية التشغيل الآن**

```bash
# الطريقة السريعة
setup-dev.bat
# ثم افتح DocumentArchive.sln في Visual Studio واضغط F5

# أو الطريقة اليدوية
build.bat
DocumentArchive.UI\bin\Debug\DocumentArchive.exe
```

---

**ملاحظة:** هذا النظام مصمم خصيصاً للبيئات العربية مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL).
