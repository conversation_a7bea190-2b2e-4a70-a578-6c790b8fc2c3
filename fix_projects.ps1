# Remove problematic <n> tags from project files
$files = @(
    "DocumentArchive.UI\DocumentArchive.UI.csproj",
    "DocumentArchive.Business\DocumentArchive.Business.csproj", 
    "DocumentArchive.Data\DocumentArchive.Data.csproj",
    "DocumentArchive.Tests\DocumentArchive.Tests.csproj"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        $lines = Get-Content $file
        $newLines = @()
        
        foreach ($line in $lines) {
            if ($line -notmatch '<n>.*</n>') {
                $newLines += $line
            } else {
                Write-Host "Removing line: $line"
            }
        }
        
        $newLines | Set-Content $file -Encoding UTF8
        Write-Host "Processed $file"
    }
}

Write-Host "All project files processed!"
