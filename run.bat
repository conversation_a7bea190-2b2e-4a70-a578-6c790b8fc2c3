@echo off
echo ========================================
echo Document Archive System - Quick Run
echo ========================================

echo.
echo Checking if application is built...

if not exist "DocumentArchive.UI\bin\Debug\DocumentArchive.exe" (
    echo Application not found. Building first...
    call build.bat
    if %errorlevel% neq 0 (
        echo ERROR: Build failed
        pause
        exit /b 1
    )
)

echo.
echo إعداد المجلدات المطلوبة...
echo Setting up required directories...
if not exist "DocumentArchive.UI\bin\Debug\Data" mkdir "DocumentArchive.UI\bin\Debug\Data"
if not exist "DocumentArchive.UI\bin\Debug\Documents" mkdir "DocumentArchive.UI\bin\Debug\Documents"
if not exist "DocumentArchive.UI\bin\Debug\Logs" mkdir "DocumentArchive.UI\bin\Debug\Logs"

echo.
echo 🚀 تشغيل نظام أرشفة الوثائق...
echo 🚀 Starting Document Archive System...
echo.

cd DocumentArchive.UI\bin\Debug
start DocumentArchive.exe

echo ✅ تم تشغيل التطبيق بنجاح!
echo ✅ Application started successfully!
echo.
echo 🎉 الميزات الجديدة المتاحة:
echo 🎉 New Features Available:
echo ✅ تصدير Excel متقدم مع رسوم بيانية
echo ✅ Advanced Excel export with charts
echo ✅ تصدير PDF مع دعم العربية
echo ✅ PDF export with Arabic support
echo ✅ لوحة معلومات تفاعلية
echo ✅ Interactive dashboard
echo ✅ بحث متقدم ومرن
echo ✅ Advanced and flexible search
echo.
echo في حالة مواجهة مشاكل:
echo If you encounter any issues:
echo 1. تحقق من مجلد Logs للتفاصيل
echo    Check the Logs folder for error details
echo 2. تأكد من تثبيت .NET Framework 4.8
echo    Ensure .NET Framework 4.8 is installed
echo 3. شغل setup-dev.bat لفحص البيئة
echo    Run setup-dev.bat for environment check
echo.
