using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DocumentArchive.Business.Helpers;
using DocumentArchive.Business.Interfaces;
using DocumentArchive.Models.DTOs;
using DocumentArchive.Models.Enums;
using NLog;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using OfficeOpenXml.Drawing.Chart;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Font = iTextSharp.text.Font;

namespace DocumentArchive.Business.Services
{
    /// <summary>
    /// Service for data export operations
    /// </summary>
    public class ExportService : IExportService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        public async Task<byte[]> ExportDocumentsToExcelAsync(List<DocumentDto> documents, string title = null)
        {
            try
            {
                return await Task.Run(() => CreateDocumentsExcel(documents, title ?? "قائمة الوثائق"));
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting documents to Excel");
                throw;
            }
        }

        public async Task<byte[]> ExportCategoriesToExcelAsync(List<CategoryDto> categories, string title = null)
        {
            try
            {
                return await Task.Run(() => CreateCategoriesExcel(categories, title ?? "قائمة الفئات"));
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting categories to Excel");
                throw;
            }
        }

        public async Task<byte[]> ExportStatisticsToExcelAsync(Dictionary<string, int> statistics, string title = null)
        {
            try
            {
                return await Task.Run(() => CreateStatisticsExcel(statistics, title ?? "إحصائيات النظام"));
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting statistics to Excel");
                throw;
            }
        }

        public async Task<byte[]> ExportDocumentsToCsvAsync(List<DocumentDto> documents)
        {
            try
            {
                var csv = await CreateDocumentsCsvAsync(documents);
                return Encoding.UTF8.GetBytes(csv);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting documents to CSV");
                throw;
            }
        }

        public async Task<byte[]> ExportCategoriesToCsvAsync(List<CategoryDto> categories)
        {
            try
            {
                var csv = await CreateCategoriesCsvAsync(categories);
                return Encoding.UTF8.GetBytes(csv);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting categories to CSV");
                throw;
            }
        }

        public async Task<byte[]> GeneratePdfReportAsync(List<DocumentDto> documents, string reportTitle, string reportType = "summary")
        {
            try
            {
                return await Task.Run(() => CreatePdfReport(documents, reportTitle, reportType));
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error generating PDF report");
                throw;
            }
        }

        public async Task<byte[]> GeneratePdfStatisticsReportAsync(Dictionary<string, int> statistics, List<CategoryDto> categories, string title = null)
        {
            try
            {
                return await Task.Run(() => CreatePdfStatisticsReport(statistics, categories, title ?? "تقرير الإحصائيات"));
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error generating PDF statistics report");
                throw;
            }
        }

        public async Task<byte[]> ExportSearchResultsToExcelAsync(SearchCriteriaDto criteria, List<DocumentDto> results)
        {
            try
            {
                var title = $"نتائج البحث - {DateTime.Now:yyyy/MM/dd}";
                var csv = await CreateSearchResultsCsvAsync(criteria, results, title);
                return Encoding.UTF8.GetBytes(csv);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error exporting search results to Excel");
                throw;
            }
        }

        public List<string> GetSupportedFormats()
        {
            return new List<string> { "Excel", "CSV", "PDF", "Text" };
        }

        public string GetFileExtension(string format)
        {
            return format.ToLower() switch
            {
                "excel" => ".xlsx",
                "csv" => ".csv",
                "pdf" => ".pdf",
                "text" => ".txt",
                _ => ".txt"
            };
        }

        public string GetMimeType(string format)
        {
            return format.ToLower() switch
            {
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "csv" => "text/csv",
                "pdf" => "application/pdf",
                "text" => "text/plain",
                _ => "text/plain"
            };
        }

        private async Task<string> CreateDocumentsCsvAsync(List<DocumentDto> documents, string title = null)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var csv = new StringBuilder();
            
            if (!string.IsNullOrEmpty(title))
            {
                csv.AppendLine($"# {title}");
                csv.AppendLine($"# تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}");
                csv.AppendLine();
            }

            // Headers
            csv.AppendLine("رقم الوثيقة,العنوان,التاريخ,النوع,المرسل/المستقبل,الفئة,الحالة,الوصف,تاريخ الإنشاء");

            // Data
            foreach (var doc in documents)
            {
                csv.AppendLine($"\"{doc.DocumentNumber}\"," +
                              $"\"{EscapeCsvValue(doc.Title)}\"," +
                              $"\"{doc.Date:yyyy/MM/dd}\"," +
                              $"\"{doc.TypeDisplayName}\"," +
                              $"\"{EscapeCsvValue(doc.SenderRecipient)}\"," +
                              $"\"{EscapeCsvValue(doc.CategoryName)}\"," +
                              $"\"{doc.StatusDisplayName}\"," +
                              $"\"{EscapeCsvValue(doc.Description)}\"," +
                              $"\"{doc.CreatedDate:yyyy/MM/dd HH:mm}\"");
            }

            return csv.ToString();
        }

        private async Task<string> CreateCategoriesCsvAsync(List<CategoryDto> categories, string title = null)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var csv = new StringBuilder();
            
            if (!string.IsNullOrEmpty(title))
            {
                csv.AppendLine($"# {title}");
                csv.AppendLine($"# تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}");
                csv.AppendLine();
            }

            // Headers
            csv.AppendLine("اسم الفئة,الوصف,نشطة,عدد الوثائق,تاريخ الإنشاء,تاريخ التعديل");

            // Data
            foreach (var category in categories)
            {
                csv.AppendLine($"\"{EscapeCsvValue(category.Name)}\"," +
                              $"\"{EscapeCsvValue(category.Description)}\"," +
                              $"\"{(category.IsActive ? "نعم" : "لا")}\"," +
                              $"\"{category.DocumentCount}\"," +
                              $"\"{category.CreatedDate:yyyy/MM/dd}\"," +
                              $"\"{category.ModifiedDate:yyyy/MM/dd}\"");
            }

            return csv.ToString();
        }

        private async Task<string> CreateStatisticsCsvAsync(Dictionary<string, int> statistics, string title = null)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var csv = new StringBuilder();
            
            if (!string.IsNullOrEmpty(title))
            {
                csv.AppendLine($"# {title}");
            }
            else
            {
                csv.AppendLine("# إحصائيات النظام");
            }
            csv.AppendLine($"# تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            csv.AppendLine();

            // Headers
            csv.AppendLine("الإحصائية,القيمة");

            // Data
            var statisticsMap = new Dictionary<string, string>
            {
                { "TotalDocuments", "إجمالي الوثائق" },
                { "IncomingDocuments", "الوثائق الواردة" },
                { "OutgoingDocuments", "الوثائق الصادرة" },
                { "DraftDocuments", "المسودات" },
                { "ProcessedDocuments", "المعالجة" },
                { "ArchivedDocuments", "المؤرشفة" }
            };

            foreach (var stat in statistics)
            {
                var displayName = statisticsMap.ContainsKey(stat.Key) ? statisticsMap[stat.Key] : stat.Key;
                csv.AppendLine($"\"{displayName}\",\"{stat.Value}\"");
            }

            return csv.ToString();
        }

        private async Task<string> CreateSearchResultsCsvAsync(SearchCriteriaDto criteria, List<DocumentDto> results, string title)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var csv = new StringBuilder();
            
            csv.AppendLine($"# {title}");
            csv.AppendLine($"# معايير البحث:");
            
            if (!string.IsNullOrEmpty(criteria.SearchText))
                csv.AppendLine($"# النص: {criteria.SearchText}");
            
            if (criteria.DateFrom.HasValue)
                csv.AppendLine($"# من تاريخ: {criteria.DateFrom:yyyy/MM/dd}");
            
            if (criteria.DateTo.HasValue)
                csv.AppendLine($"# إلى تاريخ: {criteria.DateTo:yyyy/MM/dd}");
            
            if (criteria.DocumentType.HasValue)
                csv.AppendLine($"# النوع: {NumberingHelper.GetDocumentTypeDisplayName(criteria.DocumentType.Value)}");
            
            csv.AppendLine($"# عدد النتائج: {results.Count}");
            csv.AppendLine();

            return csv.ToString() + await CreateDocumentsCsvAsync(results);
        }

        private async Task<string> CreateTextReportAsync(List<DocumentDto> documents, string reportTitle, string reportType)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var report = new StringBuilder();
            
            report.AppendLine(reportTitle);
            report.AppendLine(new string('=', reportTitle.Length));
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            report.AppendLine($"نوع التقرير: {reportType}");
            report.AppendLine();

            switch (reportType.ToLower())
            {
                case "summary":
                    CreateSummaryReport(report, documents);
                    break;
                case "detailed":
                    CreateDetailedReport(report, documents);
                    break;
                default:
                    CreateSummaryReport(report, documents);
                    break;
            }

            return report.ToString();
        }

        private void CreateSummaryReport(StringBuilder report, List<DocumentDto> documents)
        {
            report.AppendLine("ملخص الإحصائيات:");
            report.AppendLine(new string('-', 20));
            report.AppendLine($"إجمالي الوثائق: {documents.Count}");
            report.AppendLine($"الوثائق الواردة: {documents.Count(d => d.Type == DocumentType.Incoming)}");
            report.AppendLine($"الوثائق الصادرة: {documents.Count(d => d.Type == DocumentType.Outgoing)}");
            report.AppendLine();
            
            report.AppendLine($"المسودات: {documents.Count(d => d.Status == DocumentStatus.Draft)}");
            report.AppendLine($"المعالجة: {documents.Count(d => d.Status == DocumentStatus.Processed)}");
            report.AppendLine($"المؤرشفة: {documents.Count(d => d.Status == DocumentStatus.Archived)}");
            report.AppendLine();

            var topCategories = documents.GroupBy(d => d.CategoryName)
                .Select(g => new { Category = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .Take(5);

            report.AppendLine("أكثر الفئات استخداماً:");
            foreach (var cat in topCategories)
            {
                report.AppendLine($"- {cat.Category}: {cat.Count} وثيقة");
            }
        }

        private void CreateDetailedReport(StringBuilder report, List<DocumentDto> documents)
        {
            report.AppendLine("قائمة الوثائق التفصيلية:");
            report.AppendLine(new string('-', 30));

            foreach (var doc in documents.OrderByDescending(d => d.Date))
            {
                report.AppendLine($"رقم الوثيقة: {doc.DocumentNumber}");
                report.AppendLine($"العنوان: {doc.Title}");
                report.AppendLine($"التاريخ: {doc.Date:yyyy/MM/dd}");
                report.AppendLine($"النوع: {doc.TypeDisplayName}");
                report.AppendLine($"المرسل/المستقبل: {doc.SenderRecipient}");
                report.AppendLine($"الفئة: {doc.CategoryName}");
                report.AppendLine($"الحالة: {doc.StatusDisplayName}");
                
                if (!string.IsNullOrEmpty(doc.Description))
                {
                    report.AppendLine($"الوصف: {doc.Description}");
                }
                
                report.AppendLine(new string('-', 40));
            }
        }

        private async Task<string> CreateStatisticsReportAsync(Dictionary<string, int> statistics, List<CategoryDto> categories, string title)
        {
            await Task.CompletedTask; // Placeholder for async operation

            var report = new StringBuilder();
            
            report.AppendLine(title ?? "تقرير الإحصائيات");
            report.AppendLine(new string('=', (title ?? "تقرير الإحصائيات").Length));
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            report.AppendLine();

            report.AppendLine("الإحصائيات العامة:");
            report.AppendLine(new string('-', 20));
            
            var statisticsMap = new Dictionary<string, string>
            {
                { "TotalDocuments", "إجمالي الوثائق" },
                { "IncomingDocuments", "الوثائق الواردة" },
                { "OutgoingDocuments", "الوثائق الصادرة" },
                { "DraftDocuments", "المسودات" },
                { "ProcessedDocuments", "المعالجة" },
                { "ArchivedDocuments", "المؤرشفة" }
            };

            foreach (var stat in statistics)
            {
                var displayName = statisticsMap.ContainsKey(stat.Key) ? statisticsMap[stat.Key] : stat.Key;
                report.AppendLine($"{displayName}: {stat.Value}");
            }

            report.AppendLine();
            report.AppendLine("إحصائيات الفئات:");
            report.AppendLine(new string('-', 20));
            
            foreach (var category in categories.OrderByDescending(c => c.DocumentCount))
            {
                var status = category.IsActive ? "نشطة" : "غير نشطة";
                report.AppendLine($"{category.Name}: {category.DocumentCount} وثيقة ({status})");
            }

            return report.ToString();
        }

        private string EscapeCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value)) return string.Empty;

            // Escape quotes and handle line breaks
            return value.Replace("\"", "\"\"").Replace("\n", " ").Replace("\r", " ");
        }

        #region Excel Export Methods

        private byte[] CreateDocumentsExcel(List<DocumentDto> documents, string title)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("الوثائق");

                // Set RTL for Arabic
                worksheet.View.RightToLeft = true;

                // Title
                worksheet.Cells[1, 1].Value = title;
                worksheet.Cells[1, 1, 1, 8].Merge = true;
                worksheet.Cells[1, 1].Style.Font.Size = 16;
                worksheet.Cells[1, 1].Style.Font.Bold = true;
                worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Date
                worksheet.Cells[2, 1].Value = $"تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}";
                worksheet.Cells[2, 1, 2, 8].Merge = true;
                worksheet.Cells[2, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Headers
                var headers = new[] { "رقم الوثيقة", "العنوان", "التاريخ", "النوع", "المرسل/المستقبل", "الفئة", "الحالة", "تاريخ الإنشاء" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[4, i + 1].Value = headers[i];
                    worksheet.Cells[4, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[4, i + 1].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[4, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                    worksheet.Cells[4, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }

                // Data
                for (int i = 0; i < documents.Count; i++)
                {
                    var doc = documents[i];
                    var row = i + 5;

                    worksheet.Cells[row, 1].Value = doc.DocumentNumber;
                    worksheet.Cells[row, 2].Value = doc.Title;
                    worksheet.Cells[row, 3].Value = doc.Date.ToString("yyyy/MM/dd");
                    worksheet.Cells[row, 4].Value = doc.TypeDisplayName;
                    worksheet.Cells[row, 5].Value = doc.SenderRecipient;
                    worksheet.Cells[row, 6].Value = doc.CategoryName;
                    worksheet.Cells[row, 7].Value = doc.StatusDisplayName;
                    worksheet.Cells[row, 8].Value = doc.CreatedDate.ToString("yyyy/MM/dd");

                    // Add borders
                    for (int col = 1; col <= 8; col++)
                    {
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
        }

        private byte[] CreateCategoriesExcel(List<CategoryDto> categories, string title)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("الفئات");

                // Set RTL for Arabic
                worksheet.View.RightToLeft = true;

                // Title
                worksheet.Cells[1, 1].Value = title;
                worksheet.Cells[1, 1, 1, 6].Merge = true;
                worksheet.Cells[1, 1].Style.Font.Size = 16;
                worksheet.Cells[1, 1].Style.Font.Bold = true;
                worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Date
                worksheet.Cells[2, 1].Value = $"تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}";
                worksheet.Cells[2, 1, 2, 6].Merge = true;
                worksheet.Cells[2, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Headers
                var headers = new[] { "اسم الفئة", "الوصف", "نشطة", "عدد الوثائق", "تاريخ الإنشاء", "تاريخ التعديل" };
                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[4, i + 1].Value = headers[i];
                    worksheet.Cells[4, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[4, i + 1].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[4, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                    worksheet.Cells[4, i + 1].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }

                // Data
                for (int i = 0; i < categories.Count; i++)
                {
                    var category = categories[i];
                    var row = i + 5;

                    worksheet.Cells[row, 1].Value = category.Name;
                    worksheet.Cells[row, 2].Value = category.Description;
                    worksheet.Cells[row, 3].Value = category.IsActive ? "نعم" : "لا";
                    worksheet.Cells[row, 4].Value = category.DocumentCount;
                    worksheet.Cells[row, 5].Value = category.CreatedDate.ToString("yyyy/MM/dd");
                    worksheet.Cells[row, 6].Value = category.ModifiedDate.ToString("yyyy/MM/dd");

                    // Add borders
                    for (int col = 1; col <= 6; col++)
                    {
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
        }

        private byte[] CreateStatisticsExcel(Dictionary<string, int> statistics, string title)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("الإحصائيات");

                // Set RTL for Arabic
                worksheet.View.RightToLeft = true;

                // Title
                worksheet.Cells[1, 1].Value = title;
                worksheet.Cells[1, 1, 1, 3].Merge = true;
                worksheet.Cells[1, 1].Style.Font.Size = 16;
                worksheet.Cells[1, 1].Style.Font.Bold = true;
                worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Date
                worksheet.Cells[2, 1].Value = $"تاريخ التصدير: {DateTime.Now:yyyy/MM/dd HH:mm}";
                worksheet.Cells[2, 1, 2, 3].Merge = true;
                worksheet.Cells[2, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Headers
                worksheet.Cells[4, 1].Value = "الإحصائية";
                worksheet.Cells[4, 2].Value = "القيمة";
                worksheet.Cells[4, 3].Value = "النسبة المئوية";

                for (int col = 1; col <= 3; col++)
                {
                    worksheet.Cells[4, col].Style.Font.Bold = true;
                    worksheet.Cells[4, col].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[4, col].Style.Fill.BackgroundColor.SetColor(Color.Orange);
                    worksheet.Cells[4, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }

                // Statistics mapping
                var statisticsMap = new Dictionary<string, string>
                {
                    { "TotalDocuments", "إجمالي الوثائق" },
                    { "IncomingDocuments", "الوثائق الواردة" },
                    { "OutgoingDocuments", "الوثائق الصادرة" },
                    { "DraftDocuments", "المسودات" },
                    { "ProcessedDocuments", "المعالجة" },
                    { "ArchivedDocuments", "المؤرشفة" }
                };

                var totalDocs = statistics.GetValueOrDefault("TotalDocuments", 0);
                var row = 5;

                foreach (var stat in statistics)
                {
                    var displayName = statisticsMap.ContainsKey(stat.Key) ? statisticsMap[stat.Key] : stat.Key;
                    var percentage = totalDocs > 0 ? (stat.Value * 100.0 / totalDocs) : 0;

                    worksheet.Cells[row, 1].Value = displayName;
                    worksheet.Cells[row, 2].Value = stat.Value;
                    worksheet.Cells[row, 3].Value = $"{percentage:F1}%";

                    // Add borders
                    for (int col = 1; col <= 3; col++)
                    {
                        worksheet.Cells[row, col].Style.Border.BorderAround(ExcelBorderStyle.Thin);
                    }

                    row++;
                }

                // Add chart
                if (statistics.Count > 0)
                {
                    var chart = worksheet.Drawings.AddChart("إحصائيات الوثائق", eChartType.Pie);
                    chart.SetPosition(row + 2, 0, 1, 0);
                    chart.SetSize(400, 300);

                    var series = chart.Series.Add(worksheet.Cells[5, 2, row - 1, 2], worksheet.Cells[5, 1, row - 1, 1]);
                    series.Header = "إحصائيات الوثائق";
                    chart.Title.Text = "توزيع الوثائق";
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
        }

        #endregion

        #region PDF Export Methods

        private byte[] CreatePdfReport(List<DocumentDto> documents, string reportTitle, string reportType)
        {
            using (var memoryStream = new MemoryStream())
            {
                var document = new Document(PageSize.A4, 50, 50, 50, 50);
                var writer = PdfWriter.GetInstance(document, memoryStream);

                document.Open();

                // Create fonts for Arabic text
                var titleFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 18, Font.BOLD);
                var headerFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 12, Font.BOLD);
                var normalFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 10, Font.NORMAL);

                // Title
                var titleParagraph = new Paragraph(reportTitle, titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(titleParagraph);

                // Date
                var dateParagraph = new Paragraph($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(dateParagraph);

                // Summary statistics
                AddSummaryStatistics(document, documents, headerFont, normalFont);

                if (reportType.ToLower() == "detailed")
                {
                    AddDetailedDocumentsList(document, documents, headerFont, normalFont);
                }

                document.Close();
                return memoryStream.ToArray();
            }
        }

        private byte[] CreatePdfStatisticsReport(Dictionary<string, int> statistics, List<CategoryDto> categories, string title)
        {
            using (var memoryStream = new MemoryStream())
            {
                var document = new Document(PageSize.A4, 50, 50, 50, 50);
                var writer = PdfWriter.GetInstance(document, memoryStream);

                document.Open();

                // Create fonts for Arabic text
                var titleFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 18, Font.BOLD);
                var headerFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 12, Font.BOLD);
                var normalFont = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 10, Font.NORMAL);

                // Title
                var titleParagraph = new Paragraph(title, titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(titleParagraph);

                // Date
                var dateParagraph = new Paragraph($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(dateParagraph);

                // General statistics
                AddGeneralStatistics(document, statistics, headerFont, normalFont);

                // Category statistics
                AddCategoryStatistics(document, categories, headerFont, normalFont);

                document.Close();
                return memoryStream.ToArray();
            }
        }

        private void AddSummaryStatistics(Document document, List<DocumentDto> documents, Font headerFont, Font normalFont)
        {
            // Summary header
            var summaryHeader = new Paragraph("ملخص الإحصائيات", headerFont)
            {
                SpacingBefore = 20,
                SpacingAfter = 10
            };
            document.Add(summaryHeader);

            // Statistics table
            var table = new PdfPTable(2) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 70, 30 });

            // Add statistics rows
            AddStatisticRow(table, "إجمالي الوثائق", documents.Count.ToString(), normalFont);
            AddStatisticRow(table, "الوثائق الواردة", documents.Count(d => d.Type == DocumentType.Incoming).ToString(), normalFont);
            AddStatisticRow(table, "الوثائق الصادرة", documents.Count(d => d.Type == DocumentType.Outgoing).ToString(), normalFont);
            AddStatisticRow(table, "المسودات", documents.Count(d => d.Status == DocumentStatus.Draft).ToString(), normalFont);
            AddStatisticRow(table, "المعالجة", documents.Count(d => d.Status == DocumentStatus.Processed).ToString(), normalFont);
            AddStatisticRow(table, "المؤرشفة", documents.Count(d => d.Status == DocumentStatus.Archived).ToString(), normalFont);

            document.Add(table);
        }

        private void AddDetailedDocumentsList(Document document, List<DocumentDto> documents, Font headerFont, Font normalFont)
        {
            // Detailed list header
            var detailHeader = new Paragraph("قائمة الوثائق التفصيلية", headerFont)
            {
                SpacingBefore = 30,
                SpacingAfter = 10
            };
            document.Add(detailHeader);

            // Documents table
            var table = new PdfPTable(6) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 15, 25, 15, 15, 15, 15 });

            // Headers
            AddTableHeader(table, "رقم الوثيقة", normalFont);
            AddTableHeader(table, "العنوان", normalFont);
            AddTableHeader(table, "التاريخ", normalFont);
            AddTableHeader(table, "النوع", normalFont);
            AddTableHeader(table, "الفئة", normalFont);
            AddTableHeader(table, "الحالة", normalFont);

            // Data rows
            foreach (var doc in documents.OrderByDescending(d => d.Date).Take(50)) // Limit to 50 for PDF
            {
                AddTableCell(table, doc.DocumentNumber, normalFont);
                AddTableCell(table, doc.Title, normalFont);
                AddTableCell(table, doc.Date.ToString("yyyy/MM/dd"), normalFont);
                AddTableCell(table, doc.TypeDisplayName, normalFont);
                AddTableCell(table, doc.CategoryName, normalFont);
                AddTableCell(table, doc.StatusDisplayName, normalFont);
            }

            document.Add(table);

            if (documents.Count > 50)
            {
                var note = new Paragraph($"ملاحظة: تم عرض أول 50 وثيقة من أصل {documents.Count} وثيقة", normalFont)
                {
                    SpacingBefore = 10,
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(note);
            }
        }

        private void AddGeneralStatistics(Document document, Dictionary<string, int> statistics, Font headerFont, Font normalFont)
        {
            var header = new Paragraph("الإحصائيات العامة", headerFont)
            {
                SpacingBefore = 20,
                SpacingAfter = 10
            };
            document.Add(header);

            var table = new PdfPTable(3) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 50, 25, 25 });

            // Headers
            AddTableHeader(table, "الإحصائية", normalFont);
            AddTableHeader(table, "القيمة", normalFont);
            AddTableHeader(table, "النسبة المئوية", normalFont);

            var statisticsMap = new Dictionary<string, string>
            {
                { "TotalDocuments", "إجمالي الوثائق" },
                { "IncomingDocuments", "الوثائق الواردة" },
                { "OutgoingDocuments", "الوثائق الصادرة" },
                { "DraftDocuments", "المسودات" },
                { "ProcessedDocuments", "المعالجة" },
                { "ArchivedDocuments", "المؤرشفة" }
            };

            var totalDocs = statistics.GetValueOrDefault("TotalDocuments", 0);

            foreach (var stat in statistics)
            {
                var displayName = statisticsMap.ContainsKey(stat.Key) ? statisticsMap[stat.Key] : stat.Key;
                var percentage = totalDocs > 0 ? (stat.Value * 100.0 / totalDocs) : 0;

                AddTableCell(table, displayName, normalFont);
                AddTableCell(table, stat.Value.ToString(), normalFont);
                AddTableCell(table, $"{percentage:F1}%", normalFont);
            }

            document.Add(table);
        }

        private void AddCategoryStatistics(Document document, List<CategoryDto> categories, Font headerFont, Font normalFont)
        {
            var header = new Paragraph("إحصائيات الفئات", headerFont)
            {
                SpacingBefore = 30,
                SpacingAfter = 10
            };
            document.Add(header);

            var table = new PdfPTable(3) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 50, 25, 25 });

            // Headers
            AddTableHeader(table, "اسم الفئة", normalFont);
            AddTableHeader(table, "عدد الوثائق", normalFont);
            AddTableHeader(table, "الحالة", normalFont);

            foreach (var category in categories.OrderByDescending(c => c.DocumentCount))
            {
                AddTableCell(table, category.Name, normalFont);
                AddTableCell(table, category.DocumentCount.ToString(), normalFont);
                AddTableCell(table, category.IsActive ? "نشطة" : "غير نشطة", normalFont);
            }

            document.Add(table);
        }

        private void AddStatisticRow(PdfPTable table, string label, string value, Font font)
        {
            var labelCell = new PdfPCell(new Phrase(label, font))
            {
                HorizontalAlignment = Element.ALIGN_RIGHT,
                Padding = 5
            };
            table.AddCell(labelCell);

            var valueCell = new PdfPCell(new Phrase(value, font))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                Padding = 5
            };
            table.AddCell(valueCell);
        }

        private void AddTableHeader(PdfPTable table, string text, Font font)
        {
            var cell = new PdfPCell(new Phrase(text, font))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                BackgroundColor = BaseColor.LIGHT_GRAY,
                Padding = 5
            };
            table.AddCell(cell);
        }

        private void AddTableCell(PdfPTable table, string text, Font font)
        {
            var cell = new PdfPCell(new Phrase(text ?? "", font))
            {
                HorizontalAlignment = Element.ALIGN_RIGHT,
                Padding = 3
            };
            table.AddCell(cell);
        }

        #endregion
    }
}
