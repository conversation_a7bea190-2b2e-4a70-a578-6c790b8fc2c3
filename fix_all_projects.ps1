# Comprehensive Project Fix Script
# Fixes all project reference issues and ensures proper build

Write-Host "=== Document Archive System - Project Fix Script ===" -ForegroundColor Green
Write-Host "Fixing project reference issues..." -ForegroundColor Yellow

# Function to fix project references
function Fix-ProjectReferences {
    param($FilePath)
    
    if (Test-Path $FilePath) {
        Write-Host "Processing: $FilePath" -ForegroundColor Cyan
        
        # Read content
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        
        # Fix the <n> tags to <Name>
        $content = $content -replace '<n>([^<]+)</n>', '<Name>$1</Name>'
        
        # Write back
        Set-Content $FilePath -Value $content -Encoding UTF8 -NoNewline
        
        Write-Host "Fixed: $FilePath" -ForegroundColor Green
    } else {
        Write-Host "File not found: $FilePath" -ForegroundColor Red
    }
}

# Fix all project files
$projectFiles = @(
    "DocumentArchive.UI\DocumentArchive.UI.csproj",
    "DocumentArchive.Business\DocumentArchive.Business.csproj",
    "DocumentArchive.Data\DocumentArchive.Data.csproj",
    "DocumentArchive.Tests\DocumentArchive.Tests.csproj"
)

foreach ($file in $projectFiles) {
    Fix-ProjectReferences $file
}

Write-Host "`n=== Verifying NuGet Packages ===" -ForegroundColor Green

# Ensure packages directory exists
if (-not (Test-Path "packages")) {
    New-Item -ItemType Directory -Path "packages" -Force
    Write-Host "Created packages directory" -ForegroundColor Green
}

# Check if nuget.exe exists
if (-not (Test-Path "nuget.exe")) {
    Write-Host "Downloading nuget.exe..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri "https://dist.nuget.org/win-x86-commandline/latest/nuget.exe" -OutFile "nuget.exe"
        Write-Host "Downloaded nuget.exe successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to download nuget.exe: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Project Fix Complete ===" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run: .\nuget.exe restore DocumentArchive.sln" -ForegroundColor White
Write-Host "2. Build the solution using MSBuild or Visual Studio" -ForegroundColor White
Write-Host "3. Run the application" -ForegroundColor White
