# Fix project references in all .csproj files
$files = @(
    "DocumentArchive.UI\DocumentArchive.UI.csproj",
    "DocumentArchive.Business\DocumentArchive.Business.csproj", 
    "DocumentArchive.Data\DocumentArchive.Data.csproj",
    "DocumentArchive.Tests\DocumentArchive.Tests.csproj"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing $file..."
        $content = Get-Content $file -Raw -Encoding UTF8
        $content = $content -replace '<n>([^<]+)</n>', '<Name>$1</Name>'
        $content = $content -replace '<n>([^<]+)</n>', '<Name>$1</Name>'
        Set-Content $file -Value $content -Encoding UTF8 -NoNewline
        Write-Host "Fixed $file"
    }
}

Write-Host "All project files fixed!"
