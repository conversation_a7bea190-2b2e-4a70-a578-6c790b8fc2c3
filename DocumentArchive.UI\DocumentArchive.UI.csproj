﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>DocumentArchive.UI</RootNamespace>
    <AssemblyName>DocumentArchive</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <ApplicationIcon>Resources\app.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=3.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.3.1.32\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=3.1.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.3.1.32\lib\net461\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.7.15\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DocumentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DocumentForm.Designer.cs">
      <DependentUpon>DocumentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CategoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CategoryForm.Designer.cs">
      <DependentUpon>CategoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DocumentViewerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DocumentViewerForm.Designer.cs">
      <DependentUpon>DocumentViewerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ReportsForm.Designer.cs">
      <DependentUpon>ReportsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\AdvancedSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\AdvancedSearchForm.Designer.cs">
      <DependentUpon>AdvancedSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\SearchPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\SearchPanel.Designer.cs">
      <DependentUpon>SearchPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DocumentPreviewPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DocumentPreviewPanel.Designer.cs">
      <DependentUpon>DocumentPreviewPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DashboardPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DashboardPanel.Designer.cs">
      <DependentUpon>DashboardPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Helpers\UIHelper.cs" />
    <Compile Include="Helpers\MessageHelper.cs" />
    <Compile Include="Extensions\ControlExtensions.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DocumentForm.resx">
      <DependentUpon>DocumentForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CategoryForm.resx">
      <DependentUpon>CategoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\SearchPanel.resx">
      <DependentUpon>SearchPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DocumentPreviewPanel.resx">
      <DependentUpon>DocumentPreviewPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="App.config" />
    <None Include="NLog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DocumentArchive.Business\DocumentArchive.Business.csproj">
      <Project>{B2C3D4E5-F6G7-8901-BCDE-F23456789012}</Project>
      <Name>DocumentArchive.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\DocumentArchive.Data\DocumentArchive.Data.csproj">
      <Project>{C3D4E5F6-G7H8-9012-CDEF-345678901234}</Project>
      <Name>DocumentArchive.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DocumentArchive.Models\DocumentArchive.Models.csproj">
      <Project>{D4E5F6G7-H8I9-0123-DEF0-456789012345}</Project>
      <Name>DocumentArchive.Models</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
